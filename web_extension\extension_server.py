"""
Web Extension Server Module

Bu modül browser extension i<PERSON><PERSON> gerekli MCP endpoint'le<PERSON> sa<PERSON>.
Mevcut main.py'yi <PERSON>, sadece yeni opsiyonel endpoint'ler ekler.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class ExtensionServer:
    """Browser extension için MCP server endpoints"""
    
    def __init__(self, mcp_server=None):
        self.mcp_server = mcp_server
        self.registered_domains: Dict[str, Dict] = {}
        self.active_sessions: Dict[str, Dict] = {}

        # Environment'tan domain listesini al
        from web_extension import get_allowed_domains
        self.allowed_domains = get_allowed_domains()
        logger.info(f"Extension server initialized with allowed domains: {self.allowed_domains}")
        
    def register_endpoints(self):
        """MCP server'a extension endpoint'lerini kaydet"""
        if not self.mcp_server:
            logger.warning("No MCP server provided for extension endpoints")
            return
            
        # Extension registration endpoint
        @self.mcp_server.tool()
        async def register_browser_extension(
            domain: str, 
            features: List[str], 
            user_agent: str = "",
            session_id: str = ""
        ) -> Dict[str, Any]:
            """
            Browser extension'ı belirli domain için kaydet
            
            Args:
                domain: Web sitesi domain'i (örn: github.com)
                features: Aktif özellikler listesi
                user_agent: Browser user agent
                session_id: Unique session ID
            """
            try:
                # Domain whitelist kontrolü
                if not self._is_domain_allowed(domain):
                    return {
                        "success": False,
                        "error": f"Domain '{domain}' is not in whitelist",
                        "allowed_domains": list(self.registered_domains.keys())
                    }
                
                # Session kaydet
                session_data = {
                    "domain": domain,
                    "features": features,
                    "user_agent": user_agent,
                    "registered_at": datetime.now().isoformat(),
                    "last_activity": datetime.now().isoformat()
                }
                
                self.active_sessions[session_id] = session_data
                
                logger.info(f"Browser extension registered for domain: {domain}, features: {features}, session: {session_id}")
                
                return {
                    "success": True,
                    "session_id": session_id,
                    "enabled_features": features,
                    "server_version": "2.1.0-extension"
                }
                
            except Exception as e:
                logger.error(f"Extension registration failed: {str(e)}")
                return {"success": False, "error": str(e)}
        
        @self.mcp_server.tool()
        async def web_dom_event(
            session_id: str,
            event_type: str,
            event_data: Dict[str, Any],
            timestamp: str = ""
        ) -> Dict[str, Any]:
            """
            Web DOM event'lerini işle
            
            Args:
                session_id: Extension session ID
                event_type: Event tipi (dom_change, click, form_submit, etc.)
                event_data: Event detayları
                timestamp: Event zamanı
            """
            try:
                if session_id not in self.active_sessions:
                    return {"success": False, "error": "Invalid session"}
                
                # Session'ı güncelle
                self.active_sessions[session_id]["last_activity"] = datetime.now().isoformat()
                
                # Event'i işle
                processed_event = await self._process_web_event(
                    session_id, event_type, event_data, timestamp
                )
                
                logger.info(f"Web DOM event processed - session: {session_id}, type: {event_type}")
                
                return {
                    "success": True,
                    "event_id": processed_event.get("id"),
                    "analysis": processed_event.get("analysis", "")
                }
                
            except Exception as e:
                logger.error(f"Web DOM event processing failed: {str(e)}")
                return {"success": False, "error": str(e)}
        
        @self.mcp_server.tool()
        async def get_extension_config() -> Dict[str, Any]:
            """
            Extension configuration'ını al (allowed domains, features, etc.)
            """
            try:
                from web_extension import get_allowed_domains, get_enabled_features, get_extension_config

                config = get_extension_config()

                # Runtime'da güncellenen domain listesi varsa onu kullan
                current_domains = self.allowed_domains if hasattr(self, 'allowed_domains') and self.allowed_domains else get_allowed_domains()

                logger.info(f"Extension config requested. Current domains: {current_domains}")

                return {
                    "success": True,
                    "config": config,
                    "allowed_domains": current_domains,
                    "enabled_features": get_enabled_features(),
                    "server_version": "2.1.0-extension",
                    "active_sessions": len(self.active_sessions)
                }

            except Exception as e:
                logger.error(f"Failed to get extension config: {str(e)}")
                return {"success": False, "error": str(e)}

        @self.mcp_server.tool()
        async def update_domain_whitelist(domains: List[str]) -> Dict[str, Any]:
            """
            Domain whitelist'i güncelle (runtime'da)

            Args:
                domains: Yeni domain listesi
            """
            try:
                # Validate domains
                valid_domains = []
                for domain in domains:
                    domain = domain.strip().lower()
                    if domain and not domain.startswith('http'):
                        valid_domains.append(domain)

                # Always include localhost and file protocol
                essential_domains = ["localhost", "127.0.0.1", "file"]
                for essential in essential_domains:
                    if essential not in valid_domains:
                        valid_domains.append(essential)

                # Update runtime whitelist
                self.allowed_domains = valid_domains

                logger.info(f"Domain whitelist updated: {valid_domains}")

                return {
                    "success": True,
                    "updated_domains": valid_domains,
                    "message": f"Whitelist updated with {len(valid_domains)} domains",
                    "essential_domains_added": essential_domains
                }

            except Exception as e:
                logger.error(f"Failed to update domain whitelist: {str(e)}")
                return {"success": False, "error": str(e)}

        @self.mcp_server.tool()
        async def get_active_sessions() -> Dict[str, Any]:
            """
            Get all active browser extension sessions

            Returns:
                List of active sessions with their details
            """
            try:
                sessions = []
                for session_id, session_data in self.active_sessions.items():
                    sessions.append({
                        "session_id": session_id,
                        "domain": session_data.get("domain"),
                        "features": session_data.get("features", []),
                        "registered_at": session_data.get("registered_at"),
                        "last_activity": session_data.get("last_activity"),
                        "user_agent": session_data.get("user_agent", "")[:50] + "..." if len(session_data.get("user_agent", "")) > 50 else session_data.get("user_agent", "")
                    })

                return {
                    "success": True,
                    "active_sessions": sessions,
                    "total_sessions": len(sessions),
                    "message": f"Found {len(sessions)} active session(s)"
                }

            except Exception as e:
                logger.error(f"Failed to get active sessions: {str(e)}")
                return {"success": False, "error": str(e)}

        @self.mcp_server.tool()
        async def web_smart_click_auto(
            element_description: str,
            css_selector: str = "",
            confidence_threshold: float = 0.8,
            domain_filter: str = ""
        ) -> Dict[str, Any]:
            """
            Smart click with automatic session detection

            Args:
                element_description: Natural language element description
                css_selector: Optional CSS selector hint
                confidence_threshold: Minimum confidence for click
                domain_filter: Optional domain filter (e.g., 'localhost', 'file')

            Returns:
                Click result with session info
            """
            try:
                # Find active session
                target_session = None
                for session_id, session_data in self.active_sessions.items():
                    if not domain_filter or session_data.get("domain") == domain_filter:
                        target_session = (session_id, session_data)
                        break

                if not target_session:
                    available_domains = [s.get("domain") for s in self.active_sessions.values()]
                    return {
                        "success": False,
                        "error": "No active session found",
                        "available_domains": available_domains,
                        "total_sessions": len(self.active_sessions)
                    }

                session_id, session_data = target_session

                # Perform smart click using the found session
                return await self._perform_smart_click(
                    session_id=session_id,
                    element_description=element_description,
                    css_selector=css_selector,
                    confidence_threshold=confidence_threshold,
                    session_info=session_data
                )

            except Exception as e:
                logger.error(f"Auto smart click failed: {str(e)}")
                return {"success": False, "error": str(e)}

        @self.mcp_server.tool()
        async def web_smart_click(
            session_id: str,
            element_description: str,
            css_selector: str = "",
            confidence_threshold: float = 0.8
        ) -> Dict[str, Any]:
            """
            Web-specific smart click implementation
            
            Args:
                session_id: Extension session ID
                element_description: Natural language element description
                css_selector: Optional CSS selector hint
                confidence_threshold: Minimum confidence for click
            """
            try:
                if session_id not in self.active_sessions:
                    return {"success": False, "error": "Invalid session"}
                
                # Web smart click logic burada implement edilecek
                # Şimdilik placeholder
                result = {
                    "success": True,
                    "element_found": True,
                    "css_selector": css_selector or "button[data-action='save']",
                    "confidence": 0.85,
                    "click_coordinates": {"x": 100, "y": 200}
                }
                
                logger.info(f"Web smart click executed - session: {session_id}, description: {element_description}")
                
                return result

            except Exception as e:
                logger.error(f"Web smart click failed: {str(e)}")
                return {"success": False, "error": str(e)}

    async def _perform_smart_click(self, session_id: str, element_description: str,
                                 css_selector: str = "", confidence_threshold: float = 0.8,
                                 session_info: dict = None) -> Dict[str, Any]:
        """Helper method for performing smart click"""
        try:
            # Validate session
            if session_id not in self.active_sessions:
                return {"success": False, "error": "Invalid session"}

            # Update last activity
            self.active_sessions[session_id]["last_activity"] = datetime.now().isoformat()

            # For now, return mock response - real implementation would communicate with content script
            logger.info(f"Smart click requested for session {session_id}: {element_description}")

            return {
                "success": True,
                "element_found": True,
                "element_description": element_description,
                "css_selector": css_selector or f"button:contains('{element_description}')",
                "confidence": confidence_threshold + 0.05,
                "session_id": session_id,
                "domain": session_info.get("domain") if session_info else "unknown",
                "click_coordinates": {"x": 150, "y": 250},
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Smart click execution failed: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _is_domain_allowed(self, domain: str) -> bool:
        """Domain whitelist kontrolü"""
        if not domain:
            return False

        # Environment'tan domain listesi al
        from web_extension import get_allowed_domains
        allowed_domains = get_allowed_domains()

        # Runtime'da güncellenen domain listesi varsa onu kullan
        if hasattr(self, 'allowed_domains') and self.allowed_domains:
            allowed_domains = self.allowed_domains

        # File protocol için özel handling
        if domain == 'file' or domain == 'localhost':
            logger.debug(f"Domain '{domain}' allowed for file protocol or localhost")
            return True

        # Direct match kontrolü
        if domain in allowed_domains:
            logger.debug(f"Domain '{domain}' found in allowed list: {allowed_domains}")
            return True

        # Subdomain kontrolü
        for allowed in allowed_domains:
            if domain.endswith('.' + allowed):
                logger.debug(f"Domain '{domain}' allowed as subdomain of '{allowed}'")
                return True

        logger.warning(f"Domain '{domain}' not allowed. Allowed domains: {allowed_domains}")
        return False
    
    async def _process_web_event(self, session_id: str, event_type: str, 
                                event_data: Dict, timestamp: str) -> Dict[str, Any]:
        """Web event'lerini işle ve analiz et"""
        # Event processing logic burada implement edilecek
        return {
            "id": f"web_event_{datetime.now().timestamp()}",
            "type": event_type,
            "analysis": f"Processed {event_type} event for session {session_id}"
        }

# Global extension server instance
_extension_server: Optional[ExtensionServer] = None

def get_extension_server() -> Optional[ExtensionServer]:
    """Global extension server instance'ını döndür"""
    return _extension_server

def initialize_extension_server(mcp_server) -> ExtensionServer:
    """Extension server'ı initialize et"""
    global _extension_server
    _extension_server = ExtensionServer(mcp_server)
    _extension_server.register_endpoints()
    logger.info("Extension server initialized")
    return _extension_server
