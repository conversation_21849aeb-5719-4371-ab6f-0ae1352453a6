<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>ScreenMonitorMCP Extension Settings</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 8px;
        }
        
        .subtitle {
            color: #718096;
        }
        
        .section {
            background: white;
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #2d3748;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #cbd5e0;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }
        
        .domain-list {
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .domain-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 15px;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .domain-item:last-child {
            border-bottom: none;
        }
        
        .domain-name {
            font-weight: 500;
            color: #2d3748;
        }
        
        .domain-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        
        .status-default {
            background: #e6fffa;
            color: #234e52;
        }
        
        .status-custom {
            background: #e6f3ff;
            color: #1e3a8a;
        }
        
        .remove-btn {
            background: #fed7d7;
            color: #742a2a;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .remove-btn:hover {
            background: #feb2b2;
        }
        
        .button {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn-primary {
            background: #4299e1;
            color: white;
        }
        
        .btn-primary:hover {
            background: #3182ce;
        }
        
        .btn-success {
            background: #48bb78;
            color: white;
        }
        
        .btn-success:hover {
            background: #38a169;
        }
        
        .btn-secondary {
            background: #edf2f7;
            color: #4a5568;
        }
        
        .btn-secondary:hover {
            background: #e2e8f0;
        }
        
        .status-message {
            padding: 10px 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            font-weight: 500;
        }
        
        .status-success {
            background: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }
        
        .status-error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #feb2b2;
        }
        
        .help-text {
            font-size: 13px;
            color: #718096;
            margin-top: 5px;
        }
        
        .feature-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .feature-toggle:last-child {
            border-bottom: none;
        }
        
        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            background: #cbd5e0;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .toggle-switch.active {
            background: #4299e1;
        }
        
        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.2s;
        }
        
        .toggle-switch.active .toggle-slider {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">⚙️ ScreenMonitorMCP Settings</div>
        <div class="subtitle">Configure domains, features, and MCP server connection</div>
    </div>
    
    <div id="status-message" style="display: none;"></div>
    
    <div class="section">
        <div class="section-title">🌐 Domain Whitelist</div>
        <p>Add domains where the extension should be active. Only whitelisted domains can use ScreenMonitorMCP features.</p>
        
        <div class="form-group">
            <label for="new-domain">Add New Domain:</label>
            <input type="text" id="new-domain" placeholder="example.com (without http://)" />
            <div class="help-text">Enter domain without protocol (e.g., github.com, localhost, 127.0.0.1)</div>
        </div>
        
        <button id="add-domain" class="button btn-primary">Add Domain</button>
        <button id="sync-from-server" class="button btn-secondary">Sync from MCP Server</button>
        
        <div class="form-group">
            <label>Current Domains:</label>
            <div id="domain-list" class="domain-list">
                <!-- Domains will be populated by JavaScript -->
            </div>
        </div>
    </div>
    
    <div class="section">
        <div class="section-title">🔧 MCP Server Configuration</div>
        
        <div class="form-group">
            <label for="mcp-server-url">MCP Server URL:</label>
            <input type="text" id="mcp-server-url" placeholder="http://localhost:7777" />
            <div class="help-text">URL of your ScreenMonitorMCP server</div>
        </div>
        
        <button id="test-connection" class="button btn-secondary">Test Connection</button>
    </div>
    
    <div class="section">
        <div class="section-title">✨ Extension Features</div>
        
        <div class="feature-toggle">
            <div>
                <strong>DOM Monitoring</strong>
                <div class="help-text">Monitor web page changes in real-time</div>
            </div>
            <div class="toggle-switch" data-feature="dom_monitoring">
                <div class="toggle-slider"></div>
            </div>
        </div>
        
        <div class="feature-toggle">
            <div>
                <strong>Smart Click</strong>
                <div class="help-text">AI-powered element clicking with natural language</div>
            </div>
            <div class="toggle-switch" data-feature="smart_click">
                <div class="toggle-slider"></div>
            </div>
        </div>
        
        <div class="feature-toggle">
            <div>
                <strong>Text Extraction</strong>
                <div class="help-text">Extract and analyze text from web pages</div>
            </div>
            <div class="toggle-switch" data-feature="text_extraction">
                <div class="toggle-slider"></div>
            </div>
        </div>
        
        <div class="feature-toggle">
            <div>
                <strong>Event Analysis</strong>
                <div class="help-text">AI analysis of web page events and interactions</div>
            </div>
            <div class="toggle-switch" data-feature="event_analysis">
                <div class="toggle-slider"></div>
            </div>
        </div>
    </div>
    
    <div class="section">
        <button id="save-settings" class="button btn-success">💾 Save All Settings</button>
        <button id="reset-settings" class="button btn-secondary">🔄 Reset to Defaults</button>
        <button id="export-settings" class="button btn-secondary">📤 Export Settings</button>
        <input type="file" id="import-settings" accept=".json" style="display: none;" />
        <button id="import-settings-btn" class="button btn-secondary">📥 Import Settings</button>
    </div>
    
    <script src="options.js"></script>
</body>
</html>
