<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 350px;
            min-height: 400px;
            margin: 0;
            padding: 16px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            background: #f8f9fa;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .logo {
            font-size: 18px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 4px;
        }
        
        .subtitle {
            font-size: 12px;
            color: #718096;
        }
        
        .status {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            margin-bottom: 16px;
            border-radius: 8px;
            background: white;
            border: 1px solid #e2e8f0;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-connected .status-indicator {
            background: #48bb78;
        }
        
        .status-disconnected .status-indicator {
            background: #f56565;
        }
        
        .section {
            margin-bottom: 20px;
        }
        
        .section-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .domain-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            margin-bottom: 4px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
        }
        
        .domain-name {
            font-weight: 500;
            color: #2d3748;
        }
        
        .domain-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        
        .domain-active {
            background: #c6f6d5;
            color: #22543d;
        }
        
        .domain-inactive {
            background: #fed7d7;
            color: #742a2a;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            margin-bottom: 4px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
        }
        
        .feature-icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            border-radius: 3px;
        }
        
        .feature-enabled {
            background: #48bb78;
        }
        
        .feature-disabled {
            background: #a0aec0;
        }
        
        .feature-name {
            flex: 1;
            font-weight: 500;
            color: #2d3748;
        }
        
        .stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 16px;
        }
        
        .stat-item {
            text-align: center;
            padding: 12px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #2d3748;
        }
        
        .stat-label {
            font-size: 11px;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .button {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            margin-bottom: 8px;
        }
        
        .button-primary {
            background: #4299e1;
            color: white;
        }
        
        .button-primary:hover {
            background: #3182ce;
        }
        
        .button-secondary {
            background: #edf2f7;
            color: #4a5568;
        }
        
        .button-secondary:hover {
            background: #e2e8f0;
        }
        
        .footer {
            text-align: center;
            padding-top: 16px;
            border-top: 1px solid #e9ecef;
            font-size: 11px;
            color: #718096;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">ScreenMonitorMCP</div>
        <div class="subtitle">Web Extension</div>
    </div>
    
    <div id="status" class="status status-disconnected">
        <div style="display: flex; align-items: center;">
            <div class="status-indicator"></div>
            <span id="status-text">Disconnected</span>
        </div>
        <div id="current-domain" style="font-size: 12px; color: #718096;"></div>
    </div>
    
    <div class="stats">
        <div class="stat-item">
            <div id="events-count" class="stat-value">0</div>
            <div class="stat-label">Events</div>
        </div>
        <div class="stat-item">
            <div id="session-time" class="stat-value">0m</div>
            <div class="stat-label">Session</div>
        </div>
    </div>
    
    <div class="section">
        <div class="section-title">Current Domain</div>
        <div id="domain-list">
            <!-- Domains will be populated by JavaScript -->
        </div>
    </div>
    
    <div class="section">
        <div class="section-title">Features</div>
        <div id="feature-list">
            <!-- Features will be populated by JavaScript -->
        </div>
    </div>
    
    <div class="section">
        <button id="toggle-monitoring" class="button button-primary">
            Start Monitoring
        </button>
        <button id="open-options" class="button button-secondary">
            Settings
        </button>
    </div>
    
    <div class="footer">
        v1.0.0 • ScreenMonitorMCP Extension
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
