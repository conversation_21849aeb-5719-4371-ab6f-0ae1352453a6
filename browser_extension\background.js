/**
 * ScreenMonitorMCP Browser Extension - Background Service Worker
 * 
 * <PERSON>u dosya browser extension'ın background service worker'ıdır.
 * MCP server ile iletişimi yönetir ve domain whitelist kontrolü yapar.
 */

class MCPExtensionBackground {
    constructor() {
        this.mcpServerUrl = 'http://localhost:7777';
        this.sessionId = this.generateSessionId();
        this.isConnected = false;
        this.allowedDomains = [];
        this.activeTab = null;
        
        this.init();
    }
    
    async init() {
        console.log('ScreenMonitorMCP Extension Background initialized');
        
        // Extension install/startup
        chrome.runtime.onInstalled.addListener(() => {
            this.loadConfiguration();
        });
        
        // Tab activation
        chrome.tabs.onActivated.addListener((activeInfo) => {
            this.handleTabActivation(activeInfo.tabId);
        });
        
        // Tab updates
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && tab.url) {
                this.handleTabUpdate(tab);
            }
        });
        
        // Message handling from content scripts
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async response
        });
    }
    
    generateSessionId() {
        return 'ext_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    async loadConfiguration() {
        try {
            // Load configuration from storage
            const config = await chrome.storage.local.get([
                'mcpServerUrl',
                'allowedDomains',
                'extensionEnabled',
                'enabledFeatures',
                'autoSyncDomains'
            ]);
            
            this.mcpServerUrl = config.mcpServerUrl || 'http://localhost:7777';
            this.allowedDomains = config.allowedDomains || ['localhost', '127.0.0.1', 'github.com'];
            this.extensionEnabled = config.extensionEnabled !== false;
            this.enabledFeatures = config.enabledFeatures || ['dom_monitoring', 'smart_click', 'text_extraction', 'event_analysis'];
            this.autoSyncDomains = config.autoSyncDomains !== false;

            // Save default configuration if not exists
            if (!config.allowedDomains || !config.enabledFeatures) {
                const defaultConfig = {
                    allowedDomains: ['localhost', '127.0.0.1', 'github.com'],
                    extensionEnabled: true,
                    mcpServerUrl: 'http://localhost:7777',
                    enabledFeatures: ['dom_monitoring', 'smart_click', 'text_extraction', 'event_analysis'],
                    autoSyncDomains: true
                };

                await chrome.storage.local.set(defaultConfig);
                console.log('Default configuration saved');
            }

            // Auto-sync domains from server if enabled
            if (this.autoSyncDomains) {
                this.syncDomainsFromServer();
            }
            
            console.log('Configuration loaded:', {
                serverUrl: this.mcpServerUrl,
                domains: this.allowedDomains,
                enabled: this.extensionEnabled
            });
            
        } catch (error) {
            console.error('Failed to load configuration:', error);
        }
    }

    async syncDomainsFromServer() {
        try {
            console.log('Syncing domains from server...');
            const response = await fetch(`${this.mcpServerUrl}/api/extension/config`);
            if (response.ok) {
                const data = await response.json();
                console.log('Server config response:', data);

                if (data.success && data.allowed_domains) {
                    // Always include essential domains
                    const essentialDomains = ['localhost', '127.0.0.1', 'file'];
                    const serverDomains = data.allowed_domains || [];

                    // Merge server domains with existing ones and essential domains
                    const mergedDomains = [...new Set([...essentialDomains, ...this.allowedDomains, ...serverDomains])];

                    this.allowedDomains = mergedDomains;
                    await chrome.storage.local.set({ allowedDomains: mergedDomains });
                    console.log('Domains synced from server:', mergedDomains);

                    return { success: true, domains: mergedDomains };
                }
            } else {
                console.warn('Server config request failed:', response.status, response.statusText);
            }
        } catch (error) {
            console.log('Could not sync domains from server (server may be offline):', error.message);
        }

        return { success: false };
    }
    
    async handleTabActivation(tabId) {
        try {
            const tab = await chrome.tabs.get(tabId);
            this.activeTab = tab;
            
            if (this.isDomainAllowed(tab.url)) {
                await this.registerWithMCP(tab);
            }
        } catch (error) {
            console.error('Tab activation error:', error);
        }
    }
    
    async handleTabUpdate(tab) {
        if (this.isDomainAllowed(tab.url)) {
            await this.registerWithMCP(tab);
        }
    }
    
    isDomainAllowed(url) {
        if (!url) return false;

        try {
            const urlObj = new URL(url);

            // Special handling for file:// protocol (test pages)
            if (urlObj.protocol === 'file:') {
                console.log('File protocol detected, checking for file/localhost permission');
                const allowed = this.allowedDomains.includes('localhost') ||
                               this.allowedDomains.includes('file') ||
                               this.allowedDomains.includes('127.0.0.1');
                console.log('File protocol allowed:', allowed, 'Domains:', this.allowedDomains);
                return allowed;
            }

            const domain = urlObj.hostname;
            const isAllowed = this.allowedDomains.some(allowed =>
                domain === allowed || domain.endsWith('.' + allowed)
            );

            console.log(`Domain check: ${domain} -> ${isAllowed}`, 'Allowed domains:', this.allowedDomains);
            return isAllowed;
        } catch (error) {
            console.error('URL parsing error:', error);
            return false;
        }
    }
    
    async registerWithMCP(tab) {
        if (!this.extensionEnabled) {
            console.log('Extension disabled, skipping MCP registration');
            return;
        }

        try {
            const urlObj = new URL(tab.url);
            let domain = urlObj.hostname;

            // Special handling for file:// protocol
            if (urlObj.protocol === 'file:') {
                domain = 'file'; // Use 'file' domain for file:// protocol
                console.log('File protocol detected, using domain: file');
            }

            console.log(`Attempting MCP registration for domain: ${domain}, URL: ${tab.url}`);

            const features = ['dom_monitoring', 'smart_click', 'text_extraction'];

            const response = await fetch(`${this.mcpServerUrl}/api/extension/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    domain: domain,
                    features: features,
                    user_agent: navigator.userAgent,
                    session_id: this.sessionId,
                    tab_id: tab.id,
                    url: tab.url
                })
            });
            
            if (response.ok) {
                const result = await response.json();
                this.isConnected = result.success;

                console.log('MCP registration result:', result);
                console.log('🔑 Session ID for testing:', this.sessionId);

                // Notify content script
                chrome.tabs.sendMessage(tab.id, {
                    type: 'MCP_REGISTERED',
                    sessionId: this.sessionId,
                    features: features,
                    success: result.success
                });
            }
            
        } catch (error) {
            console.error('MCP registration failed:', error);
            this.isConnected = false;
        }
    }
    
    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.type) {
                case 'DOM_EVENT':
                    await this.forwardDOMEvent(message.data, sender.tab);
                    sendResponse({ success: true });
                    break;
                    
                case 'SMART_CLICK_REQUEST':
                    const clickResult = await this.handleSmartClick(message.data, sender.tab);
                    sendResponse(clickResult);
                    break;
                    
                case 'GET_CONFIG':
                    sendResponse({
                        sessionId: this.sessionId,
                        isConnected: this.isConnected,
                        allowedDomains: this.allowedDomains,
                        mcpServerUrl: this.mcpServerUrl,
                        enabledFeatures: this.enabledFeatures,
                        extensionEnabled: this.extensionEnabled
                    });
                    break;

                case 'SETTINGS_UPDATED':
                    await this.handleSettingsUpdate(message.config);
                    sendResponse({ success: true });
                    break;

                default:
                    sendResponse({ error: 'Unknown message type' });
            }
        } catch (error) {
            console.error('Message handling error:', error);
            sendResponse({ error: error.message });
        }
    }

    async handleSettingsUpdate(newConfig) {
        try {
            // Update internal configuration
            this.mcpServerUrl = newConfig.mcpServerUrl || this.mcpServerUrl;
            this.allowedDomains = newConfig.allowedDomains || this.allowedDomains;
            this.extensionEnabled = newConfig.extensionEnabled !== false;
            this.enabledFeatures = newConfig.enabledFeatures || this.enabledFeatures;
            this.autoSyncDomains = newConfig.autoSyncDomains !== false;

            console.log('Settings updated:', {
                domains: this.allowedDomains.length,
                features: this.enabledFeatures.length,
                enabled: this.extensionEnabled
            });

            // Re-register with MCP if active tab is allowed
            if (this.activeTab && this.isDomainAllowed(this.activeTab.url)) {
                await this.registerWithMCP(this.activeTab);
            }

        } catch (error) {
            console.error('Failed to update settings:', error);
        }
    }
    
    async forwardDOMEvent(eventData, tab) {
        if (!this.isConnected) return;
        
        try {
            await fetch(`${this.mcpServerUrl}/api/extension/dom-event`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    event_type: eventData.type,
                    event_data: eventData,
                    timestamp: new Date().toISOString(),
                    tab_id: tab.id,
                    url: tab.url
                })
            });
        } catch (error) {
            console.error('Failed to forward DOM event:', error);
        }
    }
    
    async handleSmartClick(clickData, tab) {
        if (!this.isConnected) return { success: false, error: 'Not connected to MCP' };
        
        try {
            const response = await fetch(`${this.mcpServerUrl}/api/extension/smart-click`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    element_description: clickData.description,
                    css_selector: clickData.selector,
                    confidence_threshold: clickData.confidence || 0.8,
                    tab_id: tab.id,
                    url: tab.url
                })
            });
            
            if (response.ok) {
                return await response.json();
            } else {
                return { success: false, error: 'MCP server error' };
            }
            
        } catch (error) {
            console.error('Smart click request failed:', error);
            return { success: false, error: error.message };
        }
    }
}

// Initialize background service worker
const mcpExtension = new MCPExtensionBackground();
