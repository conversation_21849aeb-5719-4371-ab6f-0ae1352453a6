#!/usr/bin/env python3
"""
Simple HTTP server for testing ScreenMonitorMCP Extension
"""

import http.server
import socketserver
import os
import webbrowser
from pathlib import Path

# Server configuration
PORT = 8080
HOST = "localhost"

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        # Add CORS headers for extension compatibility
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

def start_server():
    """Start the test server"""
    
    # Check if test_page.html exists
    if not Path("test_page.html").exists():
        print("❌ test_page.html not found in current directory!")
        print("Please run this script from the ScreenMonitorMCP root directory.")
        return
    
    try:
        with socketserver.TCPServer((HOST, PORT), CustomHTTPRequestHandler) as httpd:
            print("🚀 ScreenMonitorMCP Extension Test Server")
            print("=" * 50)
            print(f"📍 Server running at: http://{HOST}:{PORT}")
            print(f"🧪 Test page URL: http://{HOST}:{PORT}/test_page.html")
            print("=" * 50)
            print()
            print("📋 Test Instructions:")
            print("1. Make sure ScreenMonitorMCP extension is loaded in Chrome")
            print("2. Make sure MCP server is running with ENABLE_WEB_EXTENSION=true")
            print("3. Open the test page URL in Chrome")
            print("4. Test extension features!")
            print()
            print("🛑 Press Ctrl+C to stop the server")
            print()
            
            # Auto-open browser
            test_url = f"http://{HOST}:{PORT}/test_page.html"
            print(f"🌐 Opening {test_url} in browser...")
            webbrowser.open(test_url)
            
            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ Port {PORT} is already in use!")
            print(f"Try a different port or stop the process using port {PORT}")
        else:
            print(f"❌ Server error: {e}")

if __name__ == "__main__":
    start_server()
