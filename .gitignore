# ========================================
# ScreenMonitorMCP .gitignore
# ========================================

# Environment variables and secrets
.env
.env.local
.env.production
.env.staging
*.env

# API Keys and sensitive data
api_keys.txt
secrets.json
config.json

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# ========================================
# ScreenMonitorMCP Specific
# ========================================

# Screenshots and captured images
screenshots/
captures/
*.png
*.jpg
*.jpeg
*.gif
*.bmp
*.tiff

# User behavior data
user_behavior_data.json
behavior_patterns.json
user_patterns.json

# Temporary files
temp/
tmp/
*.tmp
*.temp

# Logs
logs/
*.log
debug.log
error.log
monitoring.log

# Cache files
cache/
.cache/
*.cache

# Test outputs
test_outputs/
test_results/

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
desktop.ini

# Linux
*~

# ========================================
# MCP Server Specific
# ========================================

# MCP client configurations (may contain sensitive paths)
mcp_config.json
client_config.json

# Runtime data
runtime/
sessions/
active_sessions.json

# Performance monitoring data
performance_data.json
monitoring_stats.json

# AI model cache
model_cache/
.transformers_cache/
.cache/huggingface/

# OCR temporary files
ocr_temp/
tesseract_temp/

# UI detection cache
ui_cache/
element_cache/
