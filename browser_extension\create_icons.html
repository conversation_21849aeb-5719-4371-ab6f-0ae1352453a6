<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator</title>
</head>
<body>
    <h2>ScreenMonitorMCP Extension Icon Generator</h2>
    <p>Bu sayfa extension için basit icon'lar o<PERSON><PERSON>.</p>
    
    <canvas id="canvas16" width="16" height="16" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="canvas32" width="32" height="32" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="canvas48" width="48" height="48" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="canvas128" width="128" height="128" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    
    <br><br>
    <button onclick="downloadIcons()">Download Icons</button>
    
    <script>
        function createIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const ctx = canvas.getContext('2d');
            
            // Background
            ctx.fillStyle = '#4299e1';
            ctx.fillRect(0, 0, size, size);
            
            // Border
            ctx.strokeStyle = '#2d3748';
            ctx.lineWidth = Math.max(1, size / 32);
            ctx.strokeRect(0, 0, size, size);
            
            // Center circle (monitor representation)
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/3, 0, 2 * Math.PI);
            ctx.fill();
            
            // Inner circle
            ctx.fillStyle = '#2d3748';
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/6, 0, 2 * Math.PI);
            ctx.fill();
            
            return canvas;
        }
        
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function downloadIcons() {
            [16, 32, 48, 128].forEach(size => {
                const canvas = createIcon(size);
                downloadCanvas(canvas, `icon${size}.png`);
            });
        }
        
        // Create icons on load
        window.onload = function() {
            [16, 32, 48, 128].forEach(size => {
                createIcon(size);
            });
        };
    </script>
</body>
</html>
